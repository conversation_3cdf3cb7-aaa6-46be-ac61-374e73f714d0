# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
ModernBERT Inference Package

A standalone inference package for ModernBERT models that provides:
- Masked Language Modeling (MLM) inference
- Text embedding generation
- Cross-platform compatibility (CUDA/MPS/CPU)
- Zero dependencies on the original training codebase

Example usage:
    ```python
    from modernbert_inference import ModernBERTInference
    
    # Initialize inference
    inference = ModernBERTInference(
        config_path="path/to/config.yaml",
        checkpoint_path="path/to/checkpoint.pt"
    )
    
    # MLM inference
    results = inference.predict_masked_tokens("The capital of France is [MASK].")
    
    # Embedding generation
    embeddings = inference.encode_texts(["Hello world", "Another sentence"])
    ```
"""

from .inference import ModernBERTInference
from .config import ModelConfig, InferenceConfig
from .pipelines import MLMPipeline, EmbeddingPipeline

__version__ = "1.0.0"
__author__ = "Answer.AI, LightOn, and contributors"
__license__ = "Apache-2.0"

__all__ = [
    "ModernBERTInference",
    "ModelConfig", 
    "InferenceConfig",
    "MLMPipeline",
    "EmbeddingPipeline",
]
